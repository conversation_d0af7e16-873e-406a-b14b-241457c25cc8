package handlers

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"

	"japancoach-backend/internal/middleware"
	"japancoach-backend/internal/models"
)

type ProgressHandler struct {
	db *gorm.DB
}

func NewProgressHandler(db *gorm.DB) *ProgressHandler {
	return &ProgressHandler{db: db}
}

// GetOverview returns a comprehensive overview of user's learning progress
func (h *ProgressHandler) GetOverview(c *gin.Context) {
	clerkUserID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get user
	var user models.User
	if err := h.db.Where("clerk_id = ?", clerkUserID).First(&user).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// Get user progress
	var progress models.UserProgress
	if err := h.db.Where("user_id = ?", user.ID).First(&progress).Error; err != nil {
		// Create initial progress if it doesn't exist
		progress = models.UserProgress{
			UserID: user.ID,
		}
		h.db.Create(&progress)
	}

	// Get vocabulary progress stats
	var vocabStats struct {
		Total    int64 `json:"total"`
		Mastered int64 `json:"mastered"`
		Learning int64 `json:"learning"`
		New      int64 `json:"new"`
	}

	h.db.Model(&models.VocabularyProgress{}).
		Where("user_id = ?", user.ID).
		Count(&vocabStats.Total)

	h.db.Model(&models.VocabularyProgress{}).
		Where("user_id = ? AND mastery_level >= ?", user.ID, 80).
		Count(&vocabStats.Mastered)

	h.db.Model(&models.VocabularyProgress{}).
		Where("user_id = ? AND mastery_level > ? AND mastery_level < ?", user.ID, 0, 80).
		Count(&vocabStats.Learning)

	vocabStats.New = vocabStats.Total - vocabStats.Mastered - vocabStats.Learning

	// Get grammar progress stats
	var grammarStats struct {
		Total    int64 `json:"total"`
		Mastered int64 `json:"mastered"`
		Learning int64 `json:"learning"`
		New      int64 `json:"new"`
	}

	h.db.Model(&models.GrammarProgress{}).
		Where("user_id = ?", user.ID).
		Count(&grammarStats.Total)

	h.db.Model(&models.GrammarProgress{}).
		Where("user_id = ? AND mastery_level >= ?", user.ID, 80).
		Count(&grammarStats.Mastered)

	h.db.Model(&models.GrammarProgress{}).
		Where("user_id = ? AND mastery_level > ? AND mastery_level < ?", user.ID, 0, 80).
		Count(&grammarStats.Learning)

	grammarStats.New = grammarStats.Total - grammarStats.Mastered - grammarStats.Learning

	// Get recent achievements
	var recentAchievements []models.UserAchievement
	h.db.Preload("Achievement").
		Where("user_id = ? AND is_completed = ?", user.ID, true).
		Order("unlocked_at DESC").
		Limit(5).
		Find(&recentAchievements)

	// Calculate next rank progress
	nextRankPoints := getNextRankPoints(user.ExperiencePoints)
	rankProgress := float64(user.ExperiencePoints) / float64(nextRankPoints) * 100

	overview := gin.H{
		"user": gin.H{
			"level":             user.JapaneseLevel,
			"current_rank":      user.CurrentRank,
			"experience_points": user.ExperiencePoints,
			"study_streak":      user.StudyStreak,
			"total_study_time":  user.TotalStudyTime,
			"rank_progress":     rankProgress,
			"next_rank_points":  nextRankPoints,
		},
		"skills": gin.H{
			"reading":   progress.ReadingLevel,
			"writing":   progress.WritingLevel,
			"listening": progress.ListeningLevel,
			"speaking":  progress.SpeakingLevel,
		},
		"lessons": gin.H{
			"completed": progress.LessonsCompleted,
		},
		"vocabulary":          vocabStats,
		"grammar":             grammarStats,
		"recent_achievements": recentAchievements,
	}

	c.JSON(http.StatusOK, overview)
}

// UpdateVocabularyProgress updates progress for a specific vocabulary word
func (h *ProgressHandler) UpdateVocabularyProgress(c *gin.Context) {
	clerkUserID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get user
	var user models.User
	if err := h.db.Where("clerk_id = ?", clerkUserID).First(&user).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	var updateData struct {
		VocabularyID string `json:"vocabulary_id" binding:"required"`
		IsCorrect    bool   `json:"is_correct"`
		ResponseTime int    `json:"response_time"` // in milliseconds
	}

	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	vocabUUID, err := uuid.Parse(updateData.VocabularyID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid vocabulary ID"})
		return
	}

	// Get or create vocabulary progress
	var vocabProgress models.VocabularyProgress
	if err := h.db.Where("user_id = ? AND vocabulary_id = ?", user.ID, vocabUUID).First(&vocabProgress).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			vocabProgress = models.VocabularyProgress{
				UserID:       user.ID,
				VocabularyID: vocabUUID,
			}
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
			return
		}
	}

	// Update progress based on spaced repetition algorithm
	now := time.Now()
	vocabProgress.TimesReviewed++
	vocabProgress.LastReviewedAt = &now

	if updateData.IsCorrect {
		vocabProgress.TimesCorrect++
		vocabProgress.ConsecutiveCorrect++

		// Increase mastery level
		vocabProgress.MasteryLevel = min(100, vocabProgress.MasteryLevel+10)

		// Calculate next review interval using spaced repetition
		switch vocabProgress.ConsecutiveCorrect {
case 1:
			vocabProgress.Interval = 1
		case 2:
			vocabProgress.Interval = 6
		default:
			vocabProgress.Interval = int(float64(vocabProgress.Interval) * vocabProgress.EaseFactor)
		}

		// Adjust ease factor
		vocabProgress.EaseFactor = max(1.3, vocabProgress.EaseFactor+0.1)
	} else {
		vocabProgress.TimesIncorrect++
		vocabProgress.ConsecutiveCorrect = 0

		// Decrease mastery level
		vocabProgress.MasteryLevel = maxInt(0, vocabProgress.MasteryLevel-5)

		// Reset interval
		vocabProgress.Interval = 1

		// Adjust ease factor
		vocabProgress.EaseFactor = max(1.3, vocabProgress.EaseFactor-0.2)
	}

	// Set next review date
	nextReview := now.AddDate(0, 0, vocabProgress.Interval)
	vocabProgress.NextReviewAt = &nextReview

	if err := h.db.Save(&vocabProgress).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update vocabulary progress"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":       "Vocabulary progress updated",
		"mastery_level": vocabProgress.MasteryLevel,
		"next_review":   vocabProgress.NextReviewAt,
		"interval":      vocabProgress.Interval,
	})
}

// UpdateGrammarProgress updates progress for a specific grammar point
func (h *ProgressHandler) UpdateGrammarProgress(c *gin.Context) {
	clerkUserID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get user
	var user models.User
	if err := h.db.Where("clerk_id = ?", clerkUserID).First(&user).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	var updateData struct {
		GrammarID    string `json:"grammar_id" binding:"required"`
		IsCorrect    bool   `json:"is_correct"`
		ExerciseType string `json:"exercise_type"` // pattern_recognition, usage, explanation
	}

	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	grammarUUID, err := uuid.Parse(updateData.GrammarID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid grammar ID"})
		return
	}

	// Get or create grammar progress
	var grammarProgress models.GrammarProgress
	if err := h.db.Where("user_id = ? AND grammar_id = ?", user.ID, grammarUUID).First(&grammarProgress).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			grammarProgress = models.GrammarProgress{
				UserID:    user.ID,
				GrammarID: grammarUUID,
			}
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
			return
		}
	}

	// Update progress
	now := time.Now()
	grammarProgress.TimesStudied++
	grammarProgress.LastStudiedAt = &now

	if updateData.IsCorrect {
		grammarProgress.TimesCorrect++
		grammarProgress.ConsecutiveCorrect++
		grammarProgress.MasteryLevel = min(100, grammarProgress.MasteryLevel+8)

		// Update understanding flags based on exercise type
		switch updateData.ExerciseType {
		case "pattern_recognition":
			grammarProgress.UnderstandsPattern = true
		case "usage":
			grammarProgress.CanUseInSentence = true
		case "explanation":
			grammarProgress.CanExplainUsage = true
		}
	} else {
		grammarProgress.TimesIncorrect++
		grammarProgress.ConsecutiveCorrect = 0
		grammarProgress.MasteryLevel = maxInt(0, grammarProgress.MasteryLevel-3)
	}

	if err := h.db.Save(&grammarProgress).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update grammar progress"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":       "Grammar progress updated",
		"mastery_level": grammarProgress.MasteryLevel,
		"understanding": gin.H{
			"pattern":     grammarProgress.UnderstandsPattern,
			"usage":       grammarProgress.CanUseInSentence,
			"explanation": grammarProgress.CanExplainUsage,
		},
	})
}

// Helper functions
func max(a, b float64) float64 {
	if a > b {
		return a
	}
	return b
}

func maxInt(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func getNextRankPoints(currentPoints int) int {
	ranks := []int{0, 100, 300, 600, 1000, 1500, 2500, 5000, 10000}

	for _, points := range ranks {
		if currentPoints < points {
			return points
		}
	}

	return ranks[len(ranks)-1] // Return highest rank if already at max
}

// GetAchievements returns all achievements and user's progress on them
func (h *ProgressHandler) GetAchievements(c *gin.Context) {
	clerkUserID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get user
	var user models.User
	if err := h.db.Where("clerk_id = ?", clerkUserID).First(&user).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// Get all achievements with user progress
	var achievements []models.Achievement
	if err := h.db.Preload("UserAchievements", "user_id = ?", user.ID).
		Where("is_active = ?", true).
		Order("category ASC, order ASC").
		Find(&achievements).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch achievements"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"achievements": achievements,
	})
}

// UnlockAchievement unlocks a specific achievement for the user
func (h *ProgressHandler) UnlockAchievement(c *gin.Context) {
	clerkUserID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get user
	var user models.User
	if err := h.db.Where("clerk_id = ?", clerkUserID).First(&user).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	achievementID := c.Param("id")
	achievementUUID, err := uuid.Parse(achievementID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid achievement ID"})
		return
	}

	// Check if achievement exists
	var achievement models.Achievement
	if err := h.db.Where("id = ?", achievementUUID).First(&achievement).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Achievement not found"})
		return
	}

	// Check if already unlocked
	var existingAchievement models.UserAchievement
	if err := h.db.Where("user_id = ? AND achievement_id = ?", user.ID, achievementUUID).First(&existingAchievement).Error; err == nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Achievement already unlocked"})
		return
	}

	// Create user achievement
	userAchievement := models.UserAchievement{
		UserID:        user.ID,
		AchievementID: achievementUUID,
		UnlockedAt:    time.Now(),
		IsCompleted:   true,
		Progress:      100,
	}

	if err := h.db.Create(&userAchievement).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to unlock achievement"})
		return
	}

	// Award experience points
	user.ExperiencePoints += achievement.Points
	user.CurrentRank = calculateRank(user.ExperiencePoints)

	if err := h.db.Save(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user experience"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":           "Achievement unlocked!",
		"achievement":       achievement,
		"experience_gained": achievement.Points,
		"new_rank":          user.CurrentRank,
	})
}
