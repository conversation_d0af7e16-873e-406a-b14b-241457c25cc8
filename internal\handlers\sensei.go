package handlers

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"

	"japancoach-backend/internal/middleware"
	"japancoach-backend/internal/models"
)

type SenseiHandler struct {
	db *gorm.DB
}

func NewSenseiHandler(db *gorm.DB) *SenseiHandler {
	return &SenseiHandler{db: db}
}

// StartConversation starts a new conversation session with AI Sensei
func (h *SenseiHandler) StartConversation(c *gin.Context) {
	clerkUserID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get user
	var user models.User
	if err := h.db.Where("clerk_id = ?", clerkUserID).First(&user).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	var requestData struct {
		SenseiType        string `json:"sensei_type" binding:"required"` // strict, encouraging, patient
		LearningObjective string `json:"learning_objective"`             // conversation, grammar, vocabulary
		Topic             string `json:"topic"`                          // Optional conversation topic
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Create new conversation session
	session := models.ConversationSession{
		UserID:        user.ID,
		SenseiType:    requestData.SenseiType,
		Topic:         requestData.Topic,
		Status:        "active",
		StartedAt:     time.Now(),
		LearningGoals: requestData.LearningObjective,
	}

	if err := h.db.Create(&session).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create conversation session"})
		return
	}

	// Create initial greeting message from sensei
	greeting := h.generateGreeting(requestData.SenseiType, requestData.LearningObjective, requestData.Topic)

	initialMessage := models.ConversationMessage{
		SessionID: session.ID,
		Role:      "sensei",
		Content:   greeting,
		Timestamp: time.Now(),
	}

	if err := h.db.Create(&initialMessage).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create initial message"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"session_id":  session.ID,
		"message":     greeting,
		"sensei_type": requestData.SenseiType,
	})
}

// CheckGrammar checks grammar and provides feedback on user's Japanese text
func (h *SenseiHandler) CheckGrammar(c *gin.Context) {
	clerkUserID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get user
	var user models.User
	if err := h.db.Where("clerk_id = ?", clerkUserID).First(&user).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	var requestData struct {
		Text      string `json:"text" binding:"required"`
		SessionID string `json:"session_id"` // Optional - if part of conversation
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Simulate grammar checking (in real implementation, this would call an AI service)
	feedback := h.analyzeGrammar(requestData.Text)

	// If part of a conversation session, save the message
	if requestData.SessionID != "" {
		sessionUUID, err := uuid.Parse(requestData.SessionID)
		if err == nil {
			// Extract score and feedback message from the gin.H map
			score, _ := feedback["score"].(float64)
			feedbackMsg, _ := feedback["feedback_message"].(string)

			// Save user message
			userMessage := models.ConversationMessage{
				SessionID:    sessionUUID,
				Role:         "user",
				Content:      requestData.Text,
				Timestamp:    time.Now(),
				GrammarScore: score,
			}
			h.db.Create(&userMessage)

			// Save sensei feedback message
			senseiMessage := models.ConversationMessage{
				SessionID: sessionUUID,
				Role:      "sensei",
				Content:   feedbackMsg,
				Timestamp: time.Now(),
			}
			h.db.Create(&senseiMessage)
		}
	}

	c.JSON(http.StatusOK, feedback)
}

// GetRecommendations provides personalized learning recommendations
func (h *SenseiHandler) GetRecommendations(c *gin.Context) {
	clerkUserID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get user with progress
	var user models.User
	if err := h.db.Preload("Progress").Where("clerk_id = ?", clerkUserID).First(&user).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// Get vocabulary that needs review
	var vocabToReview []models.VocabularyProgress
	h.db.Preload("Vocabulary").
		Where("user_id = ? AND next_review_at <= ?", user.ID, time.Now()).
		Order("next_review_at ASC").
		Limit(10).
		Find(&vocabToReview)

	// Get grammar points to study
	var grammarToStudy []models.GrammarProgress
	h.db.Preload("Grammar").
		Where("user_id = ? AND mastery_level < ?", user.ID, 80).
		Order("mastery_level ASC").
		Limit(5).
		Find(&grammarToStudy)

	// Get recommended lessons
	var recommendedLessons []models.Lesson
	h.db.Where("level = ? AND is_published = ?", user.JapaneseLevel, true).
		Order("difficulty_rating ASC").
		Limit(5).
		Find(&recommendedLessons)

	recommendations := gin.H{
		"vocabulary_review": gin.H{
			"count": len(vocabToReview),
			"items": vocabToReview,
		},
		"grammar_study": gin.H{
			"count": len(grammarToStudy),
			"items": grammarToStudy,
		},
		"recommended_lessons": gin.H{
			"count": len(recommendedLessons),
			"items": recommendedLessons,
		},
		"daily_goals": h.generateDailyGoals(user),
	}

	c.JSON(http.StatusOK, recommendations)
}

// Helper functions
func (h *SenseiHandler) generateGreeting(senseiType, objective, topic string) string {
	greetings := map[string]map[string]string{
		"strict": {
			"conversation": "こんにちは！今日は会話の練習をしましょう。正確な日本語を使うように心がけてください。",
			"grammar":      "文法の勉強を始めましょう。間違いを恐れずに挑戦してください。",
			"vocabulary":   "語彙の学習時間です。新しい単語をしっかりと覚えましょう。",
		},
		"encouraging": {
			"conversation": "こんにちは！一緒に楽しく日本語で話しましょう！間違いを恐れる必要はありません。",
			"grammar":      "文法の勉強、頑張りましょう！一歩ずつ進歩していけば大丈夫です。",
			"vocabulary":   "新しい単語を学ぶのは楽しいですね！一緒に頑張りましょう。",
		},
		"patient": {
			"conversation": "こんにちは。ゆっくりと、あなたのペースで日本語を話してみてください。",
			"grammar":      "文法は難しいですが、時間をかけて理解していきましょう。",
			"vocabulary":   "語彙学習は継続が大切です。無理をせず、少しずつ覚えていきましょう。",
		},
	}

	if greeting, exists := greetings[senseiType][objective]; exists {
		if topic != "" {
			return greeting + " 今日のトピックは「" + topic + "」です。"
		}
		return greeting
	}

	return "こんにちは！日本語の勉強を始めましょう。"
}

func (h *SenseiHandler) analyzeGrammar(text string) gin.H {
	// This is a simplified grammar analysis
	// In a real implementation, this would use NLP services or AI models

	score := 85.0 // Simulated score
	corrections := []gin.H{
		{
			"original":    "です",
			"correction":  "だ",
			"explanation": "カジュアルな文脈では「だ」を使う方が自然です。",
		},
	}

	feedbackMessage := "良い文章ですね！いくつか改善点があります。"
	if score >= 90 {
		feedbackMessage = "素晴らしい！とても自然な日本語です。"
	} else if score >= 70 {
		feedbackMessage = "良い文章ですね！いくつか改善点があります。"
	} else {
		feedbackMessage = "頑張りましたね。文法を見直してみましょう。"
	}

	return gin.H{
		"score":            score,
		"corrections":      corrections,
		"feedback_message": feedbackMessage,
		"overall_feedback": "継続して練習すれば、もっと上達します！",
	}
}

func (h *SenseiHandler) generateDailyGoals(user models.User) gin.H {
	return gin.H{
		"vocabulary_reviews":  10,
		"grammar_exercises":   3,
		"conversation_time":   15, // minutes
		"lessons_to_complete": 1,
		"study_streak_goal":   user.StudyStreak + 1,
	}
}
